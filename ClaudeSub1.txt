/**
 * Subscription Billing Calendar Automation Script
 * Manages payment events and reminders in Google Calendar based on Google Sheet data
 * Processes max 5 rows per execution to respect API quotas
 */

// Configuration
const CONFIG = {
  SHEET_NAME: 'Master',
  MAX_ROWS_PER_RUN: 5,
  EVENT_COLOR: '6', // Flamingo color
  EVENT_TITLE: 'Payment: {name}, {cost}$ ({type})',
  REMINDER_TITLE: 'Upcomming Payment: {name} {renewal_date} {cost}$ ({type})',
  SCRIPT_MARKER: '#SUBSCRIPTIONSCRIPT'
};

// Column mapping based on your sheet structure
const COLUMNS = {
  ROW_ID: 0,             // A
  SUBSCRIPTION_NAME: 1,  // B - This is the actual subscription name (YOUTUBE, NETFLIX, etc.)
  CATEGORY: 2,           // C
  SERVICE_PROVIDER: 3,   // D
  RENEWAL_DATE: 4,       // E
  BILLING_FREQUENCY: 5,  // F
  REMINDER: 6,           // G
  COST: 7,              // H
  PAYMENT_METHOD: 8,     // I
  LOGIN: 9,             // J
  STATUS: 10,           // K
  NOTE: 11,             // L
  TYPE: 12,             // M
  LAST_SYNCED_DATE: 13, // N
  LAST_SYNCED_STATUS: 14, // O
  SYNC_FLAG: 15,        // P
  STORED_HASH: 17       // R - For storing the hash/signature
};

function updateRenewalDatesBatch() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME);
    if (!sheet) {
      throw new Error(`Sheet "${CONFIG.SHEET_NAME}" not found`);
    }

    const calendar = CalendarApp.getDefaultCalendar();
    const data = sheet.getDataRange().getValues();
    
    // Skip header row
    const rows = data.slice(1);
    let processedCount = 0;
    
    for (let i = 0; i < rows.length && processedCount < CONFIG.MAX_ROWS_PER_RUN; i++) {
      const row = rows[i];
      const rowIndex = i + 2; // +2 because we skip header and array is 0-indexed
      
      // Check if row needs processing (Sync Flag = 1)
      if (row[COLUMNS.SYNC_FLAG] !== 1) continue;
      
      // Skip if essential data is missing or status is empty
      if (!row[COLUMNS.SUBSCRIPTION_NAME] || !row[COLUMNS.RENEWAL_DATE] || !row[COLUMNS.STATUS]) {
        console.log(`Skipping row ${rowIndex}: Missing essential data or empty status`);
        continue;
      }
      
      processRow(sheet, calendar, row, rowIndex);
      processedCount++;
    }
    
    console.log(`Processed ${processedCount} rows`);
    
    // Removed popup - user can check logs and see live changes
    
  } catch (error) {
    console.error('Error in updateRenewalDatesBatch:', error);
    SpreadsheetApp.getUi().alert('Error: ' + error.toString());
  }
}

function processRow(sheet, calendar, row, rowIndex) {
  const subscriptionData = {
    rowId: row[COLUMNS.ROW_ID],
    name: row[COLUMNS.SUBSCRIPTION_NAME],
    renewalDate: new Date(row[COLUMNS.RENEWAL_DATE]),
    frequency: row[COLUMNS.BILLING_FREQUENCY] || 'Monthly',
    reminder: row[COLUMNS.REMINDER] || null,
    cost: row[COLUMNS.COST] || 0,
    status: row[COLUMNS.STATUS] || 'active',
    note: row[COLUMNS.NOTE] || '',
    type: row[COLUMNS.TYPE] || '',
    lastSyncedDate: row[COLUMNS.LAST_SYNCED_DATE],
    lastSyncedStatus: row[COLUMNS.LAST_SYNCED_STATUS]
  };
  
  console.log(`Processing: ${subscriptionData.name} (Row ${rowIndex})`);
  
  // Generate unique event ID
  const eventId = generateEventId(subscriptionData);
  
  // Create hash for tracking changes
  const currentHash = createEventHash(subscriptionData);
  const lastHash = row[COLUMNS.STORED_HASH]; // Get from Stored Hash column instead of note
  
  try {
    if (subscriptionData.status.toLowerCase() === 'canceled') {
      handleCanceledSubscription(calendar, subscriptionData, eventId);
      updateStoredHash(sheet, rowIndex, 'Canceled - All future events deleted');
      
    } else if (subscriptionData.status.toLowerCase() === 'test') {
      handleTestSubscription(sheet, rowIndex, subscriptionData, eventId);
      
    } else if (subscriptionData.status.toLowerCase() === 'active') {
      // Check if renewal date is in the past and advance if needed
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (subscriptionData.renewalDate < today) {
        subscriptionData.renewalDate = advanceRenewalDate(subscriptionData.renewalDate, subscriptionData.frequency);
        // Update the renewal date in the sheet
        sheet.getRange(rowIndex, COLUMNS.RENEWAL_DATE + 1).setValue(subscriptionData.renewalDate);
      }
      
      // Delete old events if hash changed
      if (lastHash && lastHash !== currentHash) {
        deleteOldEvents(calendar, lastHash);
      }
      
      // Create new events
      createPaymentEvent(calendar, subscriptionData, eventId);
      
      if (subscriptionData.reminder) {
        createReminderEvent(calendar, subscriptionData, eventId);
      }
      
      updateStoredHash(sheet, rowIndex, currentHash);
    }
    
    // Update sync tracking
    updateSyncStatus(sheet, rowIndex, subscriptionData);
    
  } catch (error) {
    console.error(`Error processing ${subscriptionData.name}:`, error);
    updateNote(sheet, rowIndex, `Error: ${error.toString()}`);
  }
}

function generateEventId(data) {
  // Create timestamp-based unique ID: DDMMYYSUBSCRIPTIONNAME
  const now = new Date();
  const day = String(now.getDate()).padStart(2, '0');
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const year = String(now.getFullYear()).slice(-2);
  const cleanName = data.name.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
  
  return `${day}${month}${year}${cleanName}`;
}

function createEventHash(data) {
  // Create readable hash for tracking changes
  const dateStr = Utilities.formatDate(data.renewalDate, Session.getScriptTimeZone(), 'MMM dd yyyy');
  return `Payment/${data.name}/${dateStr}/${data.cost}/${data.type}/`;
}

function extractHashFromNote(note) {
  if (!note) return null;
  const match = note.match(/Payment\/.*?\//g);
  return match ? match[0] + note.split('/').slice(-2).join('/') : null;
}

function handleCanceledSubscription(calendar, data, eventId) {
  // Find and delete all future events for this subscription
  const today = new Date();
  const futureDate = new Date();
  futureDate.setFullYear(futureDate.getFullYear() + 2); // Look 2 years ahead
  
  const events = calendar.getEvents(today, futureDate);
  
  events.forEach(event => {
    const description = event.getDescription();
    if (description && description.includes(CONFIG.SCRIPT_MARKER) && 
        description.includes(eventId)) {
      event.deleteEvent();
      console.log(`Deleted event: ${event.getTitle()}`);
    }
  });
}

function handleTestSubscription(sheet, rowIndex, data, eventId) {
  const currentHash = createEventHash(data);
  const preview = `TEST MODE - Would create: ${CONFIG.EVENT_TITLE
    .replace('{name}', data.name)
    .replace('{cost}', data.cost)
    .replace('{type}', data.type)} on ${Utilities.formatDate(data.renewalDate, Session.getScriptTimeZone(), 'MMM dd, yyyy')} | ID: ${eventId}`;
  
  if (data.reminder) {
    const reminderDate = new Date(data.renewalDate);
    reminderDate.setDate(reminderDate.getDate() - parseInt(data.reminder));
    const reminderPreview = ` | Reminder: ${Utilities.formatDate(reminderDate, Session.getScriptTimeZone(), 'MMM dd, yyyy')}`;
    updateNote(sheet, rowIndex, preview + reminderPreview);
  } else {
    updateNote(sheet, rowIndex, preview);
  }
  
  // Store hash in Stored Hash column for test mode too
  updateStoredHash(sheet, rowIndex, currentHash);
}

function deleteOldEvents(calendar, oldHash) {
  // Extract the old date from hash to search for events
  const hashParts = oldHash.split('/');
  if (hashParts.length >= 3) {
    try {
      const oldDateStr = hashParts[2];
      const searchDate = new Date(oldDateStr);
      
      // Search for events around that date
      const startDate = new Date(searchDate);
      startDate.setDate(startDate.getDate() - 1);
      const endDate = new Date(searchDate);
      endDate.setDate(endDate.getDate() + 1);
      
      const events = calendar.getEvents(startDate, endDate);
      
      events.forEach(event => {
        const description = event.getDescription();
        if (description && description.includes(CONFIG.SCRIPT_MARKER) && 
            description.includes(oldHash)) {
          event.deleteEvent();
          console.log(`Deleted old event: ${event.getTitle()}`);
        }
      });
    } catch (error) {
      console.error('Error deleting old events:', error);
    }
  }
}

function createPaymentEvent(calendar, data, eventId) {
  const title = CONFIG.EVENT_TITLE
    .replace('{name}', data.name)
    .replace('{cost}', data.cost)
    .replace('{type}', data.type);
  
  const description = `${CONFIG.SCRIPT_MARKER}\nEvent ID: ${eventId}\nHash: ${createEventHash(data)}`;
  
  // Check if event already exists
  const existingEvents = calendar.getEventsForDay(data.renewalDate);
  const duplicateEvent = existingEvents.find(event => 
    event.getTitle() === title && 
    event.getDescription().includes(eventId)
  );
  
  if (!duplicateEvent) {
    const event = calendar.createAllDayEvent(title, data.renewalDate, {description: description});
    event.setColor(CONFIG.EVENT_COLOR);
    console.log(`Created payment event: ${title}`);
  } else {
    console.log(`Payment event already exists: ${title}`);
  }
}

function createReminderEvent(calendar, data, eventId) {
  const reminderDays = parseInt(data.reminder);
  if (isNaN(reminderDays) || reminderDays <= 0) return;
  
  const reminderDate = new Date(data.renewalDate);
  reminderDate.setDate(reminderDate.getDate() - reminderDays);
  
  // Don't create reminders for past dates
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  if (reminderDate < today) return;
  
  const renewalDateStr = Utilities.formatDate(data.renewalDate, Session.getScriptTimeZone(), 'yyyy-MM-dd');
  const title = CONFIG.REMINDER_TITLE
    .replace('{name}', data.name)
    .replace('{renewal_date}', renewalDateStr)
    .replace('{cost}', data.cost)
    .replace('{type}', data.type);
  
  const description = `${CONFIG.SCRIPT_MARKER}\nEvent ID: ${eventId}_reminder\nHash: ${createEventHash(data)}`;
  
  // Check if reminder already exists
  const existingEvents = calendar.getEventsForDay(reminderDate);
  const duplicateReminder = existingEvents.find(event => 
    event.getTitle() === title && 
    event.getDescription().includes(`${eventId}_reminder`)
  );
  
  if (!duplicateReminder) {
    const event = calendar.createAllDayEvent(title, reminderDate, {description: description});
    event.setColor(CONFIG.EVENT_COLOR);
    console.log(`Created reminder event: ${title}`);
  } else {
    console.log(`Reminder event already exists: ${title}`);
  }
}

function advanceRenewalDate(currentDate, frequency) {
  const newDate = new Date(currentDate);
  
  switch (frequency.toLowerCase()) {
    case 'monthly':
      newDate.setMonth(newDate.getMonth() + 1);
      break;
    case 'yearly':
      newDate.setFullYear(newDate.getFullYear() + 1);
      break;
    default:
      console.warn(`Unknown frequency: ${frequency}, defaulting to monthly`);
      newDate.setMonth(newDate.getMonth() + 1);
  }
  
  return newDate;
}

function updateNote(sheet, rowIndex, note) {
  sheet.getRange(rowIndex, COLUMNS.NOTE + 1).setValue(note);
}

function updateStoredHash(sheet, rowIndex, hash) {
  sheet.getRange(rowIndex, COLUMNS.STORED_HASH + 1).setValue(hash);
}

function updateSyncStatus(sheet, rowIndex, data) {
  const now = new Date();
  
  // Update Last Synced Date and Status
  sheet.getRange(rowIndex, COLUMNS.LAST_SYNCED_DATE + 1).setValue(now);
  sheet.getRange(rowIndex, COLUMNS.LAST_SYNCED_STATUS + 1).setValue(data.status);
  
  // The Sync Flag should automatically reset to 0 via the formula in column P
  // But let's ensure the formula is there
  const syncFlagCell = sheet.getRange(rowIndex, COLUMNS.SYNC_FLAG + 1);
  const currentFormula = syncFlagCell.getFormula();
  
  if (!currentFormula) {
    const formula = `=IF(AND(E${rowIndex}=N${rowIndex}, K${rowIndex}=O${rowIndex}), 0, 1)`;
    syncFlagCell.setFormula(formula);
  }
}

// Helper function to set up triggers (run this once manually)
function setupTrigger() {
  // Delete existing triggers first
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'updateRenewalDatesBatch') {
      ScriptApp.deleteTrigger(trigger);
    }
  });
  
  // Create new hourly trigger
  ScriptApp.newTrigger('updateRenewalDatesBatch')
    .timeBased()
    .everyHours(1)
    .create();
  
  console.log('Hourly trigger set up successfully');
}