/**
 * Subscription Billing Calendar Automation Script
 * Manages payment events and reminders in Google Calendar based on Google Sheet data
 * Processes max 5 rows per execution to respect API quotas
 */

// Configuration
const CONFIG = {
  SHEET_NAME: 'Master',
  MAX_ROWS_PER_RUN: 5,
  EVENT_COLOR: '6', // Flamingo color
  EVENT_TITLE: 'Payment: {name}, {cost}$ ({type})',
  REMINDER_TITLE: 'Upcomming Payment: {name} {renewal_date} {cost}$ ({type})',
  SCRIPT_MARKER: '#SUBSCRIPTIONSCRIPT'
};

// Column mapping based on your sheet structure (after removing N, O, P)
const COLUMNS = {
  ROW_ID: 0,             // A
  SUBSCRIPTION_NAME: 1,  // B - This is the actual subscription name (YOUTUBE, NETFLIX, etc.)
  CATEGORY: 2,           // C
  SERVICE_PROVIDER: 3,   // D
  RENEWAL_DATE: 4,       // E
  BILLING_FREQUENCY: 5,  // F
  REMINDER: 6,           // G
  COST: 7,              // H
  PAYMENT_METHOD: 8,     // I
  LOGIN: 9,             // J
  STATUS: 10,           // K
  NOTE: 11,             // L
  TYPE: 12,             // M
  LIVE_HASH: 13,        // N - Live hash formula for comparison (was Q)
  STORED_HASH: 14       // O - For storing the hash/signature (was R)
};

function updateRenewalDatesBatch() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME);
    if (!sheet) {
      throw new Error(`Sheet "${CONFIG.SHEET_NAME}" not found`);
    }

    const calendar = CalendarApp.getDefaultCalendar();
    const data = sheet.getDataRange().getValues();
    
    // Skip header row
    const rows = data.slice(1);
    let processedCount = 0;
    
    for (let i = 0; i < rows.length && processedCount < CONFIG.MAX_ROWS_PER_RUN; i++) {
      const row = rows[i];
      const rowIndex = i + 2; // +2 because we skip header and array is 0-indexed
      
      // Check if row needs processing (Q ≠ R, meaning live hash ≠ stored hash)
      const liveHash = row[COLUMNS.LIVE_HASH];
      const storedHash = row[COLUMNS.STORED_HASH];
      if (liveHash === storedHash) continue;
      
      // Skip if essential data is missing or status is empty
      if (!row[COLUMNS.SUBSCRIPTION_NAME] || !row[COLUMNS.RENEWAL_DATE] || !row[COLUMNS.STATUS]) {
        console.log(`Skipping row ${rowIndex}: Missing essential data or empty status`);
        continue;
      }
      
      processRow(sheet, calendar, row, rowIndex);
      processedCount++;
    }
    
    console.log(`Processed ${processedCount} rows`);
    
  } catch (error) {
    console.error('Error in updateRenewalDatesBatch:', error);
    SpreadsheetApp.getUi().alert('Error: ' + error.toString());
  }
}

function processRow(sheet, calendar, row, rowIndex) {
  const subscriptionData = {
    rowId: row[COLUMNS.ROW_ID],
    name: row[COLUMNS.SUBSCRIPTION_NAME],
    renewalDate: new Date(row[COLUMNS.RENEWAL_DATE]),
    frequency: row[COLUMNS.BILLING_FREQUENCY] || 'Monthly',
    reminder: row[COLUMNS.REMINDER] || null,
    cost: row[COLUMNS.COST] || 0,
    status: row[COLUMNS.STATUS] || 'active',
    note: row[COLUMNS.NOTE] || '',
    type: row[COLUMNS.TYPE] || ''
  };
  
  console.log(`Processing: ${subscriptionData.name} (Row ${rowIndex})`);
  
  // Get current event ID (don't generate here, let each handler manage it)
  let eventId = subscriptionData.rowId;
  
  // Get the stored hash to check for changes and determine action needed
  const liveHash = row[COLUMNS.LIVE_HASH];
  const storedHash = row[COLUMNS.STORED_HASH];
  
  console.log(`Live Hash: ${liveHash}`);
  console.log(`Stored Hash: ${storedHash}`);
  
  try {
    if (subscriptionData.status.toLowerCase() === 'canceled') {
      // Generate ID if needed for canceled subscriptions
      if (!eventId) {
        eventId = generateEventId(subscriptionData);
        sheet.getRange(rowIndex, COLUMNS.ROW_ID + 1).setValue(eventId);
      }
      handleCanceledSubscription(calendar, subscriptionData, eventId);
      updateStoredHash(sheet, rowIndex, 'Canceled - All future events deleted');
      
    } else if (subscriptionData.status.toLowerCase() === 'test') {
      // Generate ID if needed for test subscriptions
      if (!eventId) {
        eventId = generateEventId(subscriptionData);
        sheet.getRange(rowIndex, COLUMNS.ROW_ID + 1).setValue(eventId);
      }
      handleTestSubscription(sheet, rowIndex, subscriptionData, eventId, storedHash, liveHash);
      
    } else if (subscriptionData.status.toLowerCase() === 'active') {
      // The handleActiveSubscription function will handle ID generation internally
      handleActiveSubscription(sheet, calendar, rowIndex, subscriptionData, eventId, storedHash, liveHash);
    }
    
  } catch (error) {
    console.error(`Error processing ${subscriptionData.name}:`, error);
    updateNote(sheet, rowIndex, `Error: ${error.toString()}`);
  }
}

function handleActiveSubscription(sheet, calendar, rowIndex, subscriptionData, eventId, storedHash, liveHash) {
  // Check if renewal date is in the past and advance if needed
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  let renewalDateUpdated = false;
  if (subscriptionData.renewalDate < today) {
    subscriptionData.renewalDate = advanceRenewalDate(subscriptionData.renewalDate, subscriptionData.frequency);
    // Update the renewal date in the sheet
    sheet.getRange(rowIndex, COLUMNS.RENEWAL_DATE + 1).setValue(subscriptionData.renewalDate);
    renewalDateUpdated = true;
    console.log(`Advanced renewal date for ${subscriptionData.name} to ${subscriptionData.renewalDate}`);
  }
  
  // Generate unique event ID and store in column A if empty
  if (!eventId) {
    eventId = generateEventId(subscriptionData);
    sheet.getRange(rowIndex, COLUMNS.ROW_ID + 1).setValue(eventId);
    console.log(`Generated and stored new ID for active subscription: ${eventId}`);
  }
  
  // The current hash should match the live hash (after potential date update)
  const currentHash = createEventHash(subscriptionData);
  
  // Always delete old events when hashes don't match
  if (storedHash && storedHash !== currentHash && storedHash !== 'Canceled - All future events deleted') {
    console.log(`Hash mismatch detected - updating events`);
    deleteEventsByHash(calendar, storedHash, eventId);
  }
  
  // Always create/update events (this handles both new events and updates)
  createPaymentEvent(calendar, subscriptionData, eventId);
  
  if (subscriptionData.reminder) {
    createReminderEvent(calendar, subscriptionData, eventId);
  }
  
  // Update stored hash to match current hash (not live hash from before potential date change)
  updateStoredHash(sheet, rowIndex, currentHash);
  
  // Update sync tracking with event date (not sync date)
  updateSyncStatus(sheet, rowIndex, subscriptionData);
}

function handleTestSubscription(sheet, rowIndex, data, eventId, storedHash, liveHash) {
  // Check if renewal date is in the past and advance if needed (same logic as active)
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  if (data.renewalDate < today) {
    data.renewalDate = advanceRenewalDate(data.renewalDate, data.frequency);
    // Update the renewal date in the sheet even in test mode
    sheet.getRange(rowIndex, COLUMNS.RENEWAL_DATE + 1).setValue(data.renewalDate);
    console.log(`TEST MODE: Advanced renewal date for ${data.name} to ${data.renewalDate}`);
  }
  
  const currentHash = createEventHash(data);
  const eventDateStr = Utilities.formatDate(data.renewalDate, Session.getScriptTimeZone(), 'MMM dd, yyyy');
  
  let preview = `TEST MODE - Would create: ${CONFIG.EVENT_TITLE
    .replace('{name}', data.name)
    .replace('{cost}', data.cost)
    .replace('{type}', data.type)} on ${eventDateStr} | ID: ${eventId}`;
  
  if (data.reminder) {
    const reminderDate = new Date(data.renewalDate);
    reminderDate.setDate(reminderDate.getDate() - parseInt(data.reminder));
    const reminderDateStr = Utilities.formatDate(reminderDate, Session.getScriptTimeZone(), 'MMM dd, yyyy');
    preview += ` | Reminder: ${reminderDateStr}`;
  }
  
  // Show what would happen based on hash comparison
  if (!storedHash) {
    preview += ` | Action: CREATE NEW EVENTS`;
  } else if (liveHash !== storedHash) {
    preview += ` | Action: UPDATE EVENTS (delete old, create new)`;
    preview += ` | Old: ${storedHash} | New: ${liveHash}`;
  } else {
    preview += ` | Action: NO CHANGES NEEDED`;
  }
  
  updateNote(sheet, rowIndex, preview);
  updateStoredHash(sheet, rowIndex, currentHash);
  
  // Update sync tracking with event date (not sync date) 
  updateSyncStatus(sheet, rowIndex, data);
}

function generateEventId(data) {
  // Create timestamp-based unique ID: DDMMYYSUBSCRIPTIONNAME
  const now = new Date();
  const day = String(now.getDate()).padStart(2, '0');
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const year = String(now.getFullYear()).slice(-2);
  const cleanName = data.name.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
  
  return `${day}${month}${year}${cleanName}`;
}

function createEventHash(data) {
  // Create readable hash for tracking changes
  const dateStr = Utilities.formatDate(data.renewalDate, Session.getScriptTimeZone(), 'MMM dd yyyy');
  return `Payment/${data.name}/${dateStr}/${data.cost}/${data.type}/`;
}

function handleCanceledSubscription(calendar, data, eventId) {
  // Find and delete all future events for this subscription using the event ID
  const today = new Date();
  const futureDate = new Date();
  futureDate.setFullYear(futureDate.getFullYear() + 2); // Look 2 years ahead
  
  const events = calendar.getEvents(today, futureDate);
  
  events.forEach(event => {
    const description = event.getDescription();
    if (description && description.includes(CONFIG.SCRIPT_MARKER) && 
        description.includes(eventId)) {
      event.deleteEvent();
      console.log(`Deleted event: ${event.getTitle()}`);
    }
  });
}

function deleteEventsByHash(calendar, oldHash, eventId) {
  // Extract the old date from hash to search for events more efficiently
  const hashParts = oldHash.split('/');
  if (hashParts.length >= 3) {
    try {
      const oldDateStr = hashParts[2]; // "Jul 22 2025" format
      const searchDate = new Date(oldDateStr);
      
      // Search for events around that date (wider range to be safe)
      const startDate = new Date(searchDate);
      startDate.setDate(startDate.getDate() - 5);
      const endDate = new Date(searchDate);
      endDate.setDate(endDate.getDate() + 5);
      
      const events = calendar.getEvents(startDate, endDate);
      
      events.forEach(event => {
        const description = event.getDescription();
        // Delete events that match both the event ID and contain the old hash
        if (description && 
            description.includes(CONFIG.SCRIPT_MARKER) && 
            description.includes(eventId) &&
            description.includes(oldHash)) {
          event.deleteEvent();
          console.log(`Deleted old event: ${event.getTitle()}`);
        }
      });
    } catch (error) {
      console.error('Error deleting old events by hash:', error);
    }
  }
}

function createPaymentEvent(calendar, data, eventId) {
  const title = CONFIG.EVENT_TITLE
    .replace('{name}', data.name)
    .replace('{cost}', data.cost)
    .replace('{type}', data.type);
  
  const description = `${CONFIG.SCRIPT_MARKER}\nEvent ID: ${eventId}\nHash: ${createEventHash(data)}`;
  
  // Check if event already exists with same ID and date
  const existingEvents = calendar.getEventsForDay(data.renewalDate);
  const duplicateEvent = existingEvents.find(event => 
    event.getDescription().includes(eventId) &&
    event.getDescription().includes(CONFIG.SCRIPT_MARKER) &&
    !event.getDescription().includes('_reminder')
  );
  
  if (!duplicateEvent) {
    const event = calendar.createAllDayEvent(title, data.renewalDate, {description: description});
    event.setColor(CONFIG.EVENT_COLOR);
    console.log(`Created payment event: ${title}`);
  } else {
    console.log(`Payment event already exists: ${title}`);
  }
}

function createReminderEvent(calendar, data, eventId) {
  const reminderDays = parseInt(data.reminder);
  if (isNaN(reminderDays) || reminderDays <= 0) return;
  
  const reminderDate = new Date(data.renewalDate);
  reminderDate.setDate(reminderDate.getDate() - reminderDays);
  
  // Don't create reminders for past dates
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  if (reminderDate < today) return;
  
  const renewalDateStr = Utilities.formatDate(data.renewalDate, Session.getScriptTimeZone(), 'yyyy-MM-dd');
  const title = CONFIG.REMINDER_TITLE
    .replace('{name}', data.name)
    .replace('{renewal_date}', renewalDateStr)
    .replace('{cost}', data.cost)
    .replace('{type}', data.type);
  
  const description = `${CONFIG.SCRIPT_MARKER}\nEvent ID: ${eventId}_reminder\nHash: ${createEventHash(data)}`;
  
  // Check if reminder already exists
  const existingEvents = calendar.getEventsForDay(reminderDate);
  const duplicateReminder = existingEvents.find(event => 
    event.getDescription().includes(`${eventId}_reminder`) &&
    event.getDescription().includes(CONFIG.SCRIPT_MARKER)
  );
  
  if (!duplicateReminder) {
    const event = calendar.createAllDayEvent(title, reminderDate, {description: description});
    event.setColor(CONFIG.EVENT_COLOR);
    console.log(`Created reminder event: ${title}`);
  } else {
    console.log(`Reminder event already exists: ${title}`);
  }
}

function advanceRenewalDate(currentDate, frequency) {
  const newDate = new Date(currentDate);
  
  switch (frequency.toLowerCase()) {
    case 'monthly':
      newDate.setMonth(newDate.getMonth() + 1);
      break;
    case 'yearly':
      newDate.setFullYear(newDate.getFullYear() + 1);
      break;
    default:
      console.warn(`Unknown frequency: ${frequency}, defaulting to monthly`);
      newDate.setMonth(newDate.getMonth() + 1);
  }
  
  return newDate;
}

function updateNote(sheet, rowIndex, note) {
  sheet.getRange(rowIndex, COLUMNS.NOTE + 1).setValue(note);
}

function updateStoredHash(sheet, rowIndex, hash) {
  sheet.getRange(rowIndex, COLUMNS.STORED_HASH + 1).setValue(hash);
}

function updateSyncStatus(sheet, rowIndex, data) {
  // Ensure the Live Hash formula is in place (N column, was Q)
  const liveHashCell = sheet.getRange(rowIndex, COLUMNS.LIVE_HASH + 1);
  const currentFormula = liveHashCell.getFormula();
  
  if (!currentFormula) {
    // Create formula that concatenates: "Payment/" + B + "/" + TEXT(E,"MMM dd yyyy") + "/" + H + "/" + M + "/"
    const formula = `="Payment/"&B${rowIndex}&"/"&TEXT(E${rowIndex},"MMM dd yyyy")&"/"&H${rowIndex}&"/"&M${rowIndex}&"/"`;
    liveHashCell.setFormula(formula);
    console.log(`Set live hash formula for row ${rowIndex}`);
  }
}

// Helper function to set up triggers (run this once manually)
function setupTrigger() {
  // Delete existing triggers first
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'updateRenewalDatesBatch') {
      ScriptApp.deleteTrigger(trigger);
    }
  });
  
  // Create new hourly trigger
  ScriptApp.newTrigger('updateRenewalDatesBatch')
    .timeBased()
    .everyHours(1)
    .create();
  
  console.log('Hourly trigger set up successfully');
}

// ========== FORCE RESYNC FUNCTIONS ==========
// Add these functions for manual resync capabilities

// Method 1: Force resync specific subscription by name
function forceResyncByName(subscriptionName) {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME);
  const data = sheet.getDataRange().getValues();
  
  for (let i = 1; i < data.length; i++) { // Skip header
    if (data[i][COLUMNS.SUBSCRIPTION_NAME] === subscriptionName) {
      // Clear the stored hash to force resync
      sheet.getRange(i + 1, COLUMNS.STORED_HASH + 1).setValue('');
      console.log(`Cleared stored hash for ${subscriptionName} - will resync on next run`);
      return;
    }
  }
  console.log(`Subscription ${subscriptionName} not found`);
}

// Method 2: Force resync specific row by row number
function forceResyncByRow(rowNumber) {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME);
  // Clear the stored hash to force resync
  sheet.getRange(rowNumber, COLUMNS.STORED_HASH + 1).setValue('');
  console.log(`Cleared stored hash for row ${rowNumber} - will resync on next run`);
}

// Method 3: Force resync ALL active subscriptions
function forceResyncAll() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME);
  const data = sheet.getDataRange().getValues();
  let count = 0;
  
  for (let i = 1; i < data.length; i++) { // Skip header
    const status = data[i][COLUMNS.STATUS];
    if (status && status.toLowerCase() === 'active') {
      sheet.getRange(i + 1, COLUMNS.STORED_HASH + 1).setValue('');
      count++;
    }
  }
  console.log(`Cleared stored hash for ${count} active subscriptions - will resync on next run`);
}

// Method 4: Force immediate resync of specific subscription (bypasses queue)
function forceImmediateResync(subscriptionName) {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME);
  const calendar = CalendarApp.getDefaultCalendar();
  const data = sheet.getDataRange().getValues();
  
  for (let i = 1; i < data.length; i++) { // Skip header
    if (data[i][COLUMNS.SUBSCRIPTION_NAME] === subscriptionName) {
      const rowIndex = i + 1;
      
      // Clear stored hash first
      sheet.getRange(rowIndex, COLUMNS.STORED_HASH + 1).setValue('');
      
      // Process immediately
      processRow(sheet, calendar, data[i], rowIndex);
      console.log(`Immediately resynced ${subscriptionName}`);
      return;
    }
  }
  console.log(`Subscription ${subscriptionName} not found`);
}

/**
 * HOW TO USE THE FORCE RESYNC FUNCTIONS:
 * 
 * 1. To force resync a specific subscription:
 *    - Run: forceResyncByName("NETFLIX")
 *    - Or: forceImmediateResync("NETFLIX") for immediate processing
 * 
 * 2. To force resync a specific row:
 *    - Run: forceResyncByRow(3) // for row 3
 * 
 * 3. To force resync all active subscriptions:
 *    - Run: forceResyncAll()
 * 
 * 4. After using methods 1-3, wait for the next hourly trigger or manually run updateRenewalDatesBatch()
 */